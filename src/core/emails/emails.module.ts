import { ConfigService } from '@nestjs/config';
import { Global, Module } from '@nestjs/common';
import { EmailsService } from './emails.service';
import * as nodemailer from 'nodemailer';

export const EMAIL = 'EAMIL';

@Global()
@Module({
  providers: [
    EmailsService,
    {
      provide: EMAIL,
      useFactory(config: ConfigService) {
        return nodemailer.createTransport({
          host: config.get('EMAIL_HOST'),
          port: config.get('EMAIL_PORT'),
          secure: config.get('EMAIL_SECURE'),
          auth: {
            user: config.get('EMAIL_USER'),
            pass: config.get('EMAIL_PASS'),
          },
        });
      },
      inject: [ConfigService],
    },
  ],
  exports: [EmailsService, EMAIL],
})
export class EmailsModule {}
